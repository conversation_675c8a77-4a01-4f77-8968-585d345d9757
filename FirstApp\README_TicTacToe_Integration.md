# Piškvorky Zrzouna - React Native Integration

A complete React Native implementation of the Tic-Tac-Toe game (Piškvorky Zrzouna) integrated into the existing Expo app, converted from the original web-based version.

## 🎮 Features Implemented

### Core Game Mechanics
- **3x3 Grid**: Touch-friendly game board with responsive cell sizing
- **X/O Alternating Turns**: Classic Tic-Tac-Toe gameplay
- **Win Detection**: Automatic detection of winning combinations (rows, columns, diagonals)
- **Draw Detection**: Handles tie games when all cells are filled
- **Visual Feedback**: Winning cells are highlighted with special styling
- **Game Statistics**: Tracks wins and draws using AsyncStorage

### Mobile-Optimized Features
- **Touch Interactions**: TouchableOpacity components for all interactive elements
- **Responsive Design**: Adapts to different screen sizes using Dimensions API
- **Mobile-Friendly Sizing**: Optimized cell sizes and touch targets
- **Native Alerts**: Uses React Native Alert for game over notifications
- **Smooth Animations**: Visual feedback for button presses and winning states

### Czech Localization
- **Complete Translation**: All UI text in Czech language
- **Game Messages**: "Piškvorky Zrzouna", "<PERSON><PERSON><PERSON><PERSON>", "Nová hra", etc.
- **Alert Messages**: Win/draw notifications in Czech

## 📁 File Structure

```
FirstApp/
├── app/
│   ├── piskvorky.tsx        # Main Tic-Tac-Toe screen component
│   ├── _layout.tsx          # Updated navigation layout
│   └── index.tsx            # Updated home screen with game button
├── types/
│   └── TicTacToe.ts         # TypeScript interfaces and types
└── README_TicTacToe_Integration.md
```

## 🔧 Technical Implementation

### TypeScript Interfaces

**Core Game Types:**
```typescript
type Player = 'X' | 'O';
type CellState = '' | Player;
type GameBoard = [CellState, CellState, CellState, ...]; // 9 cells total
```

**Game State Management:**
```typescript
interface TicTacToeGameState {
  gameBoard: GameBoard;
  currentPlayer: Player;
  gameActive: boolean;
  gameStatus: 'welcome' | 'playing' | 'gameOver';
  winner: Player | null;
  winningCells: number[] | null;
  result: 'win' | 'draw' | 'ongoing';
}
```

### Game Logic Conversion

**Original Web Logic → React Native:**
- `handleCellClick(index)` → `handleCellPress(index)`
- DOM manipulation → React state updates
- CSS classes → StyleSheet objects
- HTML buttons → TouchableOpacity components
- Web alerts → React Native Alert API

**Win Detection Algorithm:**
```typescript
const WINNING_COMBINATIONS = [
  [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
  [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
  [0, 4, 8], [2, 4, 6]             // Diagonals
];
```

### State Management

**React Hooks Used:**
- `useState`: Game state and statistics management
- `useCallback`: Performance optimization for event handlers
- `useFocusEffect`: Load statistics when screen comes into focus

**AsyncStorage Integration:**
- Game statistics persistence
- Automatic loading on screen focus
- Error handling for storage operations

## 🎨 Styling & Design

### Color Scheme (Orange/White Theme)
- **Primary Orange**: `#ff6b1a` (buttons, X player)
- **Background Orange**: `#ff8c42` (gradient background)
- **Secondary Green**: `#28a745` (O player)
- **White**: `#ffffff` (cards, buttons)
- **Winning Highlight**: `#fff200` (yellow)

### Responsive Design
```typescript
const { width: screenWidth } = Dimensions.get('window');
const cellSize = Math.min((screenWidth - 80) / 3, 100);
```

### Mobile Optimizations
- Touch-friendly button sizes (minimum 44pt)
- Proper spacing between interactive elements
- Shadow effects for depth perception
- Smooth transitions and hover states

## 🚀 Navigation Integration

### Expo Router Setup
```typescript
// _layout.tsx
<Stack.Screen 
  name="piskvorky" 
  options={{
    title: 'Piškvorky Zrzouna',
    headerShown: true,
    presentation: 'card',
  }} 
/>
```

### Home Screen Integration
```typescript
// index.tsx
<Link href="./piskvorky" asChild>
  <TouchableOpacity style={[styles.button, styles.gameButton]}>
    <Text style={styles.buttonText}>🎮 Hrát Piškvorky Zrzouna</Text>
  </TouchableOpacity>
</Link>
```

## 🎯 User Experience

### Game Flow
1. **Home Screen**: Tap "🎮 Hrát Piškvorky Zrzouna" button
2. **Welcome Screen**: Shows title, play button, and statistics
3. **Game Screen**: Interactive 3x3 grid with player indicator
4. **Game Over**: Alert with options to play again or return to menu

### Touch Interactions
- **Cell Tap**: Make a move (X or O)
- **Play Button**: Start new game
- **Restart Button**: Reset current game
- **Back Button**: Return to welcome screen

### Visual Feedback
- **Active Player**: Highlighted current player indicator
- **Cell States**: Different colors for X, O, and empty cells
- **Winning Animation**: Highlighted winning cells
- **Button Press**: Visual feedback with opacity changes

## 📊 Game Statistics

### Tracked Metrics
- Total games played
- X wins
- O wins
- Draws

### Storage Implementation
```typescript
const STATS_STORAGE_KEY = '@tictactoe_game_stats';

// Save statistics
await AsyncStorage.setItem(STATS_STORAGE_KEY, JSON.stringify(stats));

// Load statistics
const storedStats = await AsyncStorage.getItem(STATS_STORAGE_KEY);
```

## 🔄 Conversion from Web Version

### Key Changes Made
1. **HTML → React Native Components**
   - `<div>` → `<View>`
   - `<button>` → `<TouchableOpacity>`
   - `<span>` → `<Text>`

2. **CSS → StyleSheet**
   - CSS classes → StyleSheet objects
   - Flexbox layout maintained
   - Shadow properties adapted for React Native

3. **DOM Events → React Native Events**
   - `onClick` → `onPress`
   - `addEventListener` → component props
   - Event handling through React state

4. **Web APIs → React Native APIs**
   - `localStorage` → `AsyncStorage`
   - `alert()` → `Alert.alert()`
   - `window.open()` → Not applicable (removed video button)

## 🛠️ Development Notes

### Performance Optimizations
- `useCallback` for event handlers
- `removeClippedSubviews` equivalent through proper state management
- Efficient re-rendering through React state updates

### Error Handling
- Try-catch blocks for AsyncStorage operations
- Graceful degradation for storage failures
- User-friendly error messages

### Accessibility
- Proper touch targets (44pt minimum)
- Clear visual hierarchy
- Semantic component structure

## 🚀 Usage Instructions

### Running the Game
1. Start Expo development server: `npx expo start`
2. Open app in Expo Go or simulator
3. Navigate to home screen
4. Tap "🎮 Hrát Piškvorky Zrzouna"

### Playing the Game
1. Tap "Hrát" to start a new game
2. Tap empty cells to make moves
3. Game automatically detects wins/draws
4. Use "Nová hra" to restart or "Zpět do menu" to return

## 🔮 Future Enhancements

Potential improvements that could be added:
- Sound effects using Expo AV
- Haptic feedback for moves
- Multiplayer functionality
- AI opponent with difficulty levels
- Custom themes and colors
- Game replay functionality
- Online leaderboards
