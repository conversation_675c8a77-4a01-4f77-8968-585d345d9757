apply plugin: 'com.android.library'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

buildscript {
  // Simple helper that allows the root project to override versions declared by this library.
  ext.safeExtGet = { prop, fallback ->
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
  }
}

group = 'host.exp.exponent'
version = '4.7.0'

android {
  namespace "expo.modules.imageloader"
  defaultConfig {
    versionCode 8
    versionName "4.7.0"
  }
}

def glideVersion = safeExtGet('glideVersion', '4.16.0')

dependencies {
  api "com.github.bumptech.glide:glide:${glideVersion}"
}
