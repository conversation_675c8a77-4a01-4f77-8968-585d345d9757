{"version": 3, "file": "BarCodeScanner.d.ts", "sourceRoot": "", "sources": ["../src/BarCodeScanner.tsx"], "names": [], "mappings": "AAAA,OAAO,EACL,kBAAkB,EAClB,gBAAgB,EAChB,qBAAqB,EAGtB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAY,SAAS,EAAE,MAAM,cAAc,CAAC;AAYnD;;;GAGG;AACH,MAAM,MAAM,YAAY,GAAG;IACzB;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;IACV;;OAEG;IACH,CAAC,EAAE,MAAM,CAAC;CACX,CAAC;AAGF,MAAM,MAAM,WAAW,GAAG;IACxB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAGF,MAAM,MAAM,aAAa,GAAG;IAC1B;;OAEG;IACH,MAAM,EAAE,YAAY,CAAC;IACrB;;OAEG;IACH,IAAI,EAAE,WAAW,CAAC;CACnB,CAAC;AAGF,MAAM,MAAM,oBAAoB,GAAG;IACjC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;;OAKG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;;;OAKG;IACH,MAAM,EAAE,aAAa,CAAC;IACtB;;;;OAIG;IACH,YAAY,EAAE,YAAY,EAAE,CAAC;CAC9B,CAAC;AAGF,MAAM,MAAM,YAAY,GAAG,oBAAoB,GAAG;IAChD,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAGF,MAAM,MAAM,6BAA6B,GAAG;IAC1C,WAAW,EAAE,YAAY,CAAC;CAC3B,CAAC;AAGF,MAAM,MAAM,sBAAsB,GAAG,CAAC,MAAM,EAAE,YAAY,KAAK,IAAI,CAAC;AAGpE,MAAM,MAAM,mBAAmB,GAAG,SAAS,GAAG;IAC5C;;;;OAIG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;IACjC;;;;;;;OAOG;IACH,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IACxB;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,sBAAsB,CAAC;CAC3C,CAAC;AAEF;;;;;GAKG;AACH,qBAAa,cAAe,SAAQ,KAAK,CAAC,SAAS,CAAC,mBAAmB,CAAC;IACtE,UAAU,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAM;IACxC,eAAe,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAM;IAE7C,MAAM,CAAC,SAAS;;;MAGd;IAEF,MAAM,CAAC,gBAAgB;;MAErB;IAEF,MAAM,CAAC,YAAY;;;MAGjB;IAEF,iBAAiB,IAAI,IAAI;IAUzB;;;OAGG;WACU,mBAAmB,IAAI,OAAO,CAAC,kBAAkB,CAAC;IAK/D;;;;;OAKG;WACU,uBAAuB,IAAI,OAAO,CAAC,kBAAkB,CAAC;IAKnE;;;;;;;;OAQG;IACH,MAAM,CAAC,cAAc,6JAGlB;IAGH;;;;;;;;;OASG;WACU,gBAAgB,CAC3B,GAAG,EAAE,MAAM,EACX,YAAY,GAAE,MAAM,EAA+B,GAClD,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAqBlC,MAAM;IAWN;;OAEG;IACH,gBAAgB,cACF,sBAAsB,uBAChB,6BAA6B,UAgB7C;IAEJ;;OAEG;IACH,kBAAkB,CAAC,KAAK,EAAE,mBAAmB;CAa9C;AAED,OAAO,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,CAAC;AACvE,eAAO,MAAQ,SAAS;;;GAAE,mBAAmB,6CAAE,uBAAuB,iDAAE,gBAAgB,wCACxE,CAAC"}