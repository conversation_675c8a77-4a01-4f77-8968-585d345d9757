// app/tabs/scanner.tsx
import React, { useEffect, useState } from 'react';
import { Text, View, Button, StyleSheet, Alert } from 'react-native';
import { BarCodeScanner } from 'expo-barcode-scanner';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function ScannerScreen() {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [scanned, setScanned] = useState(false);

  useEffect(() => {
    (async () => {
      const { status } = await BarCodeScanner.requestPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  const handleBarCodeScanned = async ({ data }: any) => {
    setScanned(true);
    Alert.alert('Naskenováno', `Produkt: ${data}`);

    try {
      const existing = await AsyncStorage.getItem('products');
      const items = existing ? JSON.parse(existing) : [];
      const updated = [...items, data];
      await AsyncStorage.setItem('products', JSON.stringify(updated));
    } catch (err) {
      console.error('Chyba při ukládání produktu:', err);
    }
  };

  if (hasPermission === null) return <Text>Žádost o přístup ke kameře…</Text>;
  if (hasPermission === false) return <Text>Přístup ke kameře zamítnut.</Text>;

  return (
    <View style={{ flex: 1 }}>
      <BarCodeScanner
        onBarCodeScanned={scanned ? undefined : handleBarCodeScanned}
        style={StyleSheet.absoluteFillObject}
      />
      {scanned && <Button title="Skenovat znovu" onPress={() => setScanned(false)} />}
    </View>
  );
}
