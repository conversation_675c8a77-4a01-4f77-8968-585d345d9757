apply plugin: 'com.android.library'

group = 'host.exp.exponent'
version = '13.0.1'

def expoModulesCorePlugin = new File(project(":expo-modules-core").projectDir.absolutePath, "ExpoModulesCorePlugin.gradle")
apply from: expoModulesCorePlugin
applyKotlinExpoModulesCorePlugin()
useCoreDependencies()
useDefaultAndroidSdkVersions()
useExpoPublishing()

android {
  namespace "expo.modules.barcodescanner"
  defaultConfig {
    versionCode 26
    versionName "13.0.1"
  }
}

dependencies {
  api 'com.google.mlkit:barcode-scanning:17.1.0'
  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.3")
  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1")
}
