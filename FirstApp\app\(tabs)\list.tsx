// app/tabs/list.tsx
import React, { useCallback, useState } from 'react';
import { View, Text, FlatList, Button, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from 'expo-router';

export default function ListScreen() {
  const [items, setItems] = useState<string[]>([]);

  const loadItems = async () => {
    const stored = await AsyncStorage.getItem('products');
    if (stored) setItems(JSON.parse(stored));
  };

  const clearItems = async () => {
    await AsyncStorage.removeItem('products');
    setItems([]);
  };

  useFocusEffect(
    useCallback(() => {
      loadItems();
    }, [])
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🛒 Nákupní seznam</Text>
      <FlatList
        data={items}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item }) => <Text style={styles.item}>• {item}</Text>}
      />
      <Button title="🧹 Smazat vše" onPress={clearItems} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20 },
  title: { fontSize: 24, marginBottom: 15, fontWeight: 'bold' },
  item: { padding: 10, borderBottomWidth: 1, borderColor: '#ddd' },
});
