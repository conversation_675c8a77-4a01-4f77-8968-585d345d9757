import { Stack } from "expo-router";

/**
 * Root Layout Component
 * Defines the navigation structure for the entire application
 * Uses expo-router's Stack navigator for screen transitions
 */
export default function RootLayout() {
  return (
    <Stack
      screenOptions={{
        // Global screen options
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: '#ffffff',
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 18,
        },
        headerBackTitleVisible: false,
        animation: 'slide_from_right',
      }}
    >
      {/* Home/Index Screen */}
      <Stack.Screen 
        name="index" 
        options={{
          title: 'FirstApp',
          headerShown: true,
        }} 
      />
      
      {/* List Screen */}
      <Stack.Screen 
        name="list" 
        options={{
          title: '<PERSON>z<PERSON> položek',
          headerShown: true,
          presentation: 'card',
        }} 
      />
    </Stack>
  );
}
