"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
Object.defineProperty(exports, "useAsyncStorage", {
  enumerable: true,
  get: function () {
    return _hooks.useAsyncStorage;
  }
});
var _AsyncStorage = _interopRequireDefault(require("./AsyncStorage"));
var _hooks = require("./hooks");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
var _default = exports.default = _AsyncStorage.default;
//# sourceMappingURL=index.js.map