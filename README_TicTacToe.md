# Pi<PERSON><PERSON><PERSON><PERSON> - Tic-Tac-Toe Game

A modern, beautiful web-based Tic-Tac-Toe game with Czech localization and orange/white color scheme.

## Features

### 🎮 Game Features
- **Classic Tic-Tac-Toe gameplay** - X and O alternating turns
- **Win detection** - Automatic detection of winning combinations
- **Draw detection** - Handles tie games
- **Visual feedback** - Winning cells are highlighted
- **Game statistics** - Tracks wins and draws (stored locally)

### 🎨 Modern UI Design
- **Orange and white color scheme** - Beautiful gradient background
- **Rounded corners and shadows** - Modern, clean aesthetic
- **Smooth animations** - Hover effects and transitions
- **Responsive design** - Works on desktop, tablet, and mobile
- **Accessibility support** - Keyboard navigation and screen reader friendly

### 🔊 Audio Features
- **Sound effects** - Click sounds, win sounds, and draw sounds
- **Sound toggle** - Mute/unmute button in bottom right
- **Web Audio API** - Generated sound effects (no external files needed)

### 🎯 User Interface
- **Welcome screen** - Large "Hrát" (Play) button to start
- **Game board** - 3x3 grid with hover effects
- **Player indicator** - Shows current player's turn
- **Game controls** - Restart game and back to menu buttons
- **Modal dialogs** - Game over notifications with options

### 🎬 Additional Features
- **Video button** - Opens YouTube video in new tab (bottom left)
- **Keyboard support** - Use number keys 1-9 to play, Escape to go back
- **Local storage** - Saves game statistics between sessions

## Files Structure

```
├── index.html      # Main HTML structure
├── styles.css      # Modern CSS styling
└── script.js       # Game logic and interactions
```

## How to Play

1. **Start the game** - Click the "Hrát" button on the welcome screen
2. **Make moves** - Click on empty cells or use number keys 1-9
3. **Win condition** - Get three X's or O's in a row (horizontal, vertical, or diagonal)
4. **Game controls** - Use restart button or return to main menu
5. **Sound control** - Toggle sound on/off with the speaker button

## Technical Details

### Technologies Used
- **HTML5** - Semantic markup with accessibility features
- **CSS3** - Modern styling with flexbox, grid, and animations
- **Vanilla JavaScript** - No external dependencies
- **Web Audio API** - For sound effects
- **Local Storage** - For persistent game statistics

### Browser Compatibility
- Modern browsers supporting ES6+
- Web Audio API support required for sound effects
- Responsive design works on all screen sizes

### Accessibility Features
- **ARIA labels** - Screen reader support
- **Keyboard navigation** - Full keyboard control
- **Focus indicators** - Clear visual focus states
- **Semantic HTML** - Proper heading structure and roles

## Customization

The game can be easily customized by modifying:
- **Colors** - Change the orange theme in `styles.css`
- **Language** - Update text strings in `index.html` and `script.js`
- **Sounds** - Modify frequency and duration in sound functions
- **Layout** - Adjust responsive breakpoints and sizing

## Installation

Simply open `index.html` in any modern web browser. No server or build process required.

## License

This is a demonstration project created for educational purposes.
