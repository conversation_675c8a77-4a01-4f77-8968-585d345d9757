/**
 * TypeScript interfaces for the List Items screen component
 * Defines the data structures used throughout the application
 */

/**
 * Interface representing a single list item
 * @interface ListItem
 */
export interface ListItem {
  /** Unique identifier for the list item */
  id: string;
  /** Display title of the list item */
  title: string;
  /** Timestamp when the item was created */
  createdAt: Date;
  /** Optional timestamp when the item was last modified */
  updatedAt?: Date;
}

/**
 * Interface for the component's state management
 * @interface ListScreenState
 */
export interface ListScreenState {
  /** Array of list items to display */
  items: ListItem[];
  /** Loading state indicator */
  isLoading: boolean;
  /** Error state for handling failures */
  error: string | null;
  /** Indicates if data is being refreshed */
  isRefreshing: boolean;
}

/**
 * Interface for AsyncStorage operations
 * @interface StorageOperations
 */
export interface StorageOperations {
  /** Load items from AsyncStorage */
  loadItems: () => Promise<ListItem[]>;
  /** Save items to AsyncStorage */
  saveItems: (items: ListItem[]) => Promise<void>;
  /** Add a single item to storage */
  addItem: (title: string) => Promise<ListItem>;
  /** Clear all items from storage */
  clearItems: () => Promise<void>;
}

/**
 * Props interface for the ListItem component
 * @interface ListItemProps
 */
export interface ListItemProps {
  /** The list item data to display */
  item: ListItem;
  /** Index of the item in the list */
  index: number;
  /** Optional callback for item press events */
  onPress?: (item: ListItem) => void;
  /** Optional callback for item long press events */
  onLongPress?: (item: ListItem) => void;
}

/**
 * Constants for AsyncStorage keys and configuration
 */
export const STORAGE_KEYS = {
  /** Key for storing the list items in AsyncStorage */
  LIST_ITEMS: '@list_items_storage_key',
} as const;

/**
 * Configuration constants for the list component
 */
export const LIST_CONFIG = {
  /** Maximum number of items to display */
  MAX_ITEMS: 1000,
  /** Default item title prefix */
  DEFAULT_TITLE_PREFIX: 'Položka',
  /** Animation duration for list updates */
  ANIMATION_DURATION: 300,
} as const;
