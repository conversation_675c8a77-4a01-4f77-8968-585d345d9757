{"version": 3, "file": "ExpoBarCodeScannerModule.web.js", "sourceRoot": "", "sources": ["../src/ExpoBarCodeScannerModule.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,gBAAgB,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAE9F,SAAS,YAAY,CAAC,WAAmC;IACvD,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE;QACjE,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;KACzD;IAED,iFAAiF;IACjF,+DAA+D;IAC/D,oEAAoE;IAEpE,yDAAyD;IACzD,MAAM,YAAY;IAChB,yHAAyH;IACzH,SAAS,CAAC,YAAY;QACtB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,eAAe;QACzB;YACE,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACzD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;YACf,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC;YAC/B,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;IAEJ,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,uBAAuB,CAAC,EAAE,OAAO,EAAuB;IAC/D,wBAAwB;IACxB,UAAU;IACV,IAAI,OAAO,KAAK,sBAAsB,EAAE;QACtC,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;YACrC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,KAAK;SACf,CAAC;KACH;SAAM;QACL,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;YAC/B,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,KAAK;SACf,CAAC;KACH;AACH,CAAC;AAED,KAAK,UAAU,6BAA6B;IAC1C,IAAI;QACF,MAAM,YAAY,CAAC;YACjB,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,OAAO;YACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;YAChC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;SACd,CAAC;KACH;IAAC,OAAO,EAAE,OAAO,EAAE,EAAE;QACpB,OAAO,uBAAuB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;KAC7C;AACH,CAAC;AAED,KAAK,UAAU,2BAA2B;IACxC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE;QAClC,MAAM,IAAI,mBAAmB,CAC3B,sBAAsB,EACtB,4CAA4C,CAC7C,CAAC;KACH;IAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;IACxE,QAAQ,KAAK,EAAE;QACb,KAAK,QAAQ;YACX,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,YAAY;gBACrC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,KAAK,SAAS;YACZ,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,OAAO;gBAChC,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,KAAK,QAAQ;YACX,OAAO;gBACL,MAAM,EAAE,gBAAgB,CAAC,MAAM;gBAC/B,OAAO,EAAE,OAAO;gBAChB,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,KAAK;aACf,CAAC;KACL;AACH,CAAC;AAED,eAAe;IACb,IAAI,WAAW;QACb,OAAO;YACL,WAAW,EAAE,aAAa;YAC1B,OAAO,EAAE,SAAS;YAClB,eAAe,EAAE,iBAAiB;YAClC,KAAK,EAAE,OAAO;YACd,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,MAAM;YACZ,EAAE,EAAE,IAAI;YACR,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,OAAO;YACd,UAAU,EAAE,YAAY;YACxB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,SAAS;YAClB,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,OAAO;SACf,CAAC;IACJ,CAAC;IACD,IAAI,IAAI;QACN,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC1C,CAAC;IACD,KAAK,CAAC,uBAAuB;QAC3B,OAAO,6BAA6B,EAAE,CAAC;IACzC,CAAC;IACD,KAAK,CAAC,mBAAmB;QACvB,OAAO,2BAA2B,EAAE,CAAC;IACvC,CAAC;CACF,CAAC", "sourcesContent": ["import { PermissionResponse, PermissionStatus, UnavailabilityError } from 'expo-modules-core';\n\nfunction getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n\n  // First get ahold of the legacy getUserMedia, if present\n  const getUserMedia =\n    // TODO: this method is deprecated, migrate to https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n    navigator.getUserMedia ||\n    navigator.webkitGetUserMedia ||\n    navigator.mozGetUserMedia ||\n    function () {\n      const error: any = new Error('Permission unimplemented');\n      error.code = 0;\n      error.name = 'NotAllowedError';\n      throw error;\n    };\n\n  return new Promise((resolve, reject) => {\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\n\nfunction handleGetUserMediaError({ message }: { message: string }): PermissionResponse {\n  // name: NotAllowedError\n  // code: 0\n  if (message === 'Permission dismissed') {\n    return {\n      status: PermissionStatus.UNDETERMINED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false,\n    };\n  } else {\n    return {\n      status: PermissionStatus.DENIED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: false,\n    };\n  }\n}\n\nasync function handleRequestPermissionsAsync(): Promise<PermissionResponse> {\n  try {\n    await getUserMedia({\n      video: true,\n    });\n    return {\n      status: PermissionStatus.GRANTED,\n      expires: 'never',\n      canAskAgain: true,\n      granted: true,\n    };\n  } catch ({ message }) {\n    return handleGetUserMediaError({ message });\n  }\n}\n\nasync function handlePermissionsQueryAsync(): Promise<PermissionResponse> {\n  if (!navigator?.permissions?.query) {\n    throw new UnavailabilityError(\n      'expo-barcode-scanner',\n      'navigator.permissions API is not available'\n    );\n  }\n\n  const { state } = await navigator.permissions.query({ name: 'camera' });\n  switch (state) {\n    case 'prompt':\n      return {\n        status: PermissionStatus.UNDETERMINED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n    case 'granted':\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true,\n      };\n    case 'denied':\n      return {\n        status: PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n  }\n}\n\nexport default {\n  get BarCodeType() {\n    return {\n      code39mod43: 'code39mod43',\n      code138: 'code138',\n      interleaved2of5: 'interleaved2of5',\n      aztec: 'aztec',\n      ean13: 'ean13',\n      ean8: 'ean8',\n      qr: 'qr',\n      pdf417: 'pdf417',\n      upc_e: 'upc_e',\n      datamatrix: 'datamatrix',\n      code39: 'code39',\n      code93: 'code93',\n      itf14: 'itf14',\n      codabar: 'codabar',\n      code128: 'code128',\n      upc_a: 'upc_a',\n    };\n  },\n  get Type() {\n    return { front: 'front', back: 'back' };\n  },\n  async requestPermissionsAsync(): Promise<PermissionResponse> {\n    return handleRequestPermissionsAsync();\n  },\n  async getPermissionsAsync(): Promise<PermissionResponse> {\n    return handlePermissionsQueryAsync();\n  },\n};\n"]}