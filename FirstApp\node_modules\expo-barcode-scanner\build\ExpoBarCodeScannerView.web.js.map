{"version": 3, "file": "ExpoBarCodeScannerView.web.js", "sourceRoot": "", "sources": ["../src/ExpoBarCodeScannerView.web.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAE1C,MAAM,CAAC,OAAO,OAAO,sBAAuB,SAAQ,KAAK,CAAC,SAAiB;IACzE,MAAM;QACJ,OAAO,CACL,CAAC,IAAI,CACH;QAAA,CAAC,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAC/D;MAAA,EAAE,IAAI,CAAC,CACR,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["import React from 'react';\nimport { Text, View } from 'react-native';\n\nexport default class ExpoBarCodeScannerView extends React.Component<object> {\n  render() {\n    return (\n      <View>\n        <Text>BarCodeScanner Component not supported on the web</Text>\n      </View>\n    );\n  }\n}\n"]}