/**
 * TypeScript interfaces for the Tic-Tac-Toe game (Piškvorky Zrzouna)
 * Defines the data structures and types used throughout the game
 */

/**
 * Player type - either X or O
 */
export type Player = 'X' | 'O';

/**
 * Cell state - can be empty string, X, or O
 */
export type CellState = '' | Player;

/**
 * Game board - array of 9 cells representing the 3x3 grid
 */
export type GameBoard = [
  CellState, CellState, CellState,
  CellState, CellState, CellState,
  CellState, CellState, CellState
];

/**
 * Game result types
 */
export type GameResult = 'win' | 'draw' | 'ongoing';

/**
 * Game status for UI display
 */
export type GameStatus = 'welcome' | 'playing' | 'gameOver';

/**
 * Winning combination - array of 3 cell indices
 */
export type WinningCombination = [number, number, number];

/**
 * Interface for the main game state
 */
export interface TicTacToeGameState {
  /** Current game board state */
  gameBoard: GameBoard;
  /** Current player's turn */
  currentPlayer: Player;
  /** Whether the game is currently active */
  gameActive: boolean;
  /** Current game status for UI */
  gameStatus: GameStatus;
  /** Winner of the game (if any) */
  winner: Player | null;
  /** Winning combination indices (if game is won) */
  winningCells: number[] | null;
  /** Game result */
  result: GameResult;
}

/**
 * Interface for game statistics
 */
export interface GameStatistics {
  /** Number of X wins */
  xWins: number;
  /** Number of O wins */
  oWins: number;
  /** Number of draws */
  draws: number;
  /** Total games played */
  totalGames: number;
}

/**
 * Interface for game cell props
 */
export interface GameCellProps {
  /** Cell index (0-8) */
  index: number;
  /** Current cell value */
  value: CellState;
  /** Whether this cell is part of winning combination */
  isWinning: boolean;
  /** Callback when cell is pressed */
  onPress: (index: number) => void;
  /** Whether the game is active */
  gameActive: boolean;
}

/**
 * Interface for game controls props
 */
export interface GameControlsProps {
  /** Current game status */
  gameStatus: GameStatus;
  /** Current player */
  currentPlayer: Player;
  /** Game result */
  result: GameResult;
  /** Winner (if any) */
  winner: Player | null;
  /** Callback to start new game */
  onStartGame: () => void;
  /** Callback to restart current game */
  onRestartGame: () => void;
  /** Callback to go back to menu */
  onBackToMenu: () => void;
}

/**
 * Winning combinations for Tic-Tac-Toe
 * Rows, columns, and diagonals
 */
export const WINNING_COMBINATIONS: WinningCombination[] = [
  // Rows
  [0, 1, 2],
  [3, 4, 5],
  [6, 7, 8],
  // Columns
  [0, 3, 6],
  [1, 4, 7],
  [2, 5, 8],
  // Diagonals
  [0, 4, 8],
  [2, 4, 6],
];

/**
 * Game configuration constants
 */
export const GAME_CONFIG = {
  /** Total number of cells in the game board */
  BOARD_SIZE: 9,
  /** Number of cells in a row/column */
  GRID_SIZE: 3,
  /** Starting player */
  STARTING_PLAYER: 'X' as Player,
  /** AsyncStorage key for game statistics */
  STATS_STORAGE_KEY: '@tictactoe_game_stats',
} as const;

/**
 * Game messages in Czech
 */
export const GAME_MESSAGES = {
  TITLE: 'Piškvorky Zrzouna',
  PLAY_BUTTON: 'Hrát',
  RESTART_BUTTON: 'Nová hra',
  BACK_TO_MENU: 'Zpět do menu',
  CURRENT_PLAYER: 'Hráč na tahu',
  PLAYER_X_WINS: 'Vyhrál hráč X!',
  PLAYER_O_WINS: 'Vyhrál hráč O!',
  DRAW: 'Remíza!',
  CONGRATULATIONS: 'Gratulujeme!',
  DRAW_MESSAGE: 'Nikdo nevyhrál. Zkuste to znovu!',
  PLAY_AGAIN: 'Hrát znovu',
  MAIN_MENU: 'Hlavní menu',
} as const;

/**
 * Utility type for game actions
 */
export type GameAction = 
  | { type: 'START_GAME' }
  | { type: 'MAKE_MOVE'; payload: { index: number } }
  | { type: 'RESTART_GAME' }
  | { type: 'BACK_TO_MENU' }
  | { type: 'SET_GAME_STATE'; payload: Partial<TicTacToeGameState> };

/**
 * Helper function type for checking win condition
 */
export type CheckWinFunction = (board: GameBoard) => {
  hasWinner: boolean;
  winner: Player | null;
  winningCells: number[] | null;
};

/**
 * Helper function type for checking draw condition
 */
export type CheckDrawFunction = (board: GameBoard) => boolean;
