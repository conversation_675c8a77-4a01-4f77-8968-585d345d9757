import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from 'expo-router';
import { ListItem, ListScreenState, STORAGE_KEYS, LIST_CONFIG } from '../types/ListItem';

/**
 * List Screen Component
 * Displays and manages a list of items with AsyncStorage persistence
 * Uses useFocusEffect to reload data when screen comes into focus
 */
export default function ListScreen() {
  // State management for the list screen
  const [state, setState] = useState<ListScreenState>({
    items: [],
    isLoading: true,
    error: null,
    isRefreshing: false,
  });

  /**
   * Load items from AsyncStorage
   * Handles JSON parsing and error cases gracefully
   */
  const loadItems = useCallback(async (): Promise<void> => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      // Retrieve data from AsyncStorage
      const storedData = await AsyncStorage.getItem(STORAGE_KEYS.LIST_ITEMS);
      
      if (storedData) {
        // Parse stored JSON data and convert date strings back to Date objects
        const parsedItems: ListItem[] = JSON.parse(storedData).map((item: any) => ({
          ...item,
          createdAt: new Date(item.createdAt),
          updatedAt: item.updatedAt ? new Date(item.updatedAt) : undefined,
        }));
        
        setState(prev => ({
          ...prev,
          items: parsedItems,
          isLoading: false,
        }));
      } else {
        // No data found, initialize with empty array
        setState(prev => ({
          ...prev,
          items: [],
          isLoading: false,
        }));
      }
    } catch (error) {
      console.error('Error loading items:', error);
      setState(prev => ({
        ...prev,
        error: 'Chyba při načítání položek',
        isLoading: false,
      }));
    }
  }, []);

  /**
   * Save items to AsyncStorage
   * Converts the items array to JSON format for storage
   */
  const saveItems = useCallback(async (items: ListItem[]): Promise<void> => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.LIST_ITEMS, JSON.stringify(items));
    } catch (error) {
      console.error('Error saving items:', error);
      throw new Error('Chyba při ukládání položek');
    }
  }, []);

  /**
   * Add a new item to the list
   * Creates a new item with unique ID and current timestamp
   */
  const addItem = useCallback(async (): Promise<void> => {
    try {
      // Check if we've reached the maximum number of items
      if (state.items.length >= LIST_CONFIG.MAX_ITEMS) {
        Alert.alert('Limit dosažen', `Můžete mít maximálně ${LIST_CONFIG.MAX_ITEMS} položek.`);
        return;
      }

      // Generate unique ID using timestamp and random number
      const newId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Create new item with default title
      const newItem: ListItem = {
        id: newId,
        title: `${LIST_CONFIG.DEFAULT_TITLE_PREFIX} ${state.items.length + 1}`,
        createdAt: new Date(),
      };

      // Update state with new item (add to beginning of list)
      const updatedItems = [newItem, ...state.items];
      
      setState(prev => ({
        ...prev,
        items: updatedItems,
      }));

      // Save to AsyncStorage
      await saveItems(updatedItems);
      
      // Show success feedback
      Alert.alert('Úspěch', 'Položka byla přidána!');
      
    } catch (error) {
      console.error('Error adding item:', error);
      Alert.alert('Chyba', 'Nepodařilo se přidat položku.');
    }
  }, [state.items, saveItems]);

  /**
   * Handle pull-to-refresh functionality
   * Reloads data from AsyncStorage
   */
  const handleRefresh = useCallback(async (): Promise<void> => {
    setState(prev => ({ ...prev, isRefreshing: true }));
    await loadItems();
    setState(prev => ({ ...prev, isRefreshing: false }));
  }, [loadItems]);

  /**
   * Handle item press events
   * Currently shows item details in an alert
   */
  const handleItemPress = useCallback((item: ListItem): void => {
    const createdDate = item.createdAt.toLocaleDateString('cs-CZ');
    const createdTime = item.createdAt.toLocaleTimeString('cs-CZ');
    
    Alert.alert(
      item.title,
      `Vytvořeno: ${createdDate} v ${createdTime}\nID: ${item.id}`,
      [{ text: 'OK', style: 'default' }]
    );
  }, []);

  /**
   * useFocusEffect hook to load data when screen comes into focus
   * This ensures data is always fresh when user navigates to this screen
   */
  useFocusEffect(
    useCallback(() => {
      loadItems();
    }, [loadItems])
  );

  /**
   * Render individual list item
   * Displays item title and creation date
   */
  const renderItem = useCallback(({ item, index }: { item: ListItem; index: number }) => (
    <TouchableOpacity
      style={styles.listItem}
      onPress={() => handleItemPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.itemContent}>
        <Text style={styles.itemTitle}>{item.title}</Text>
        <Text style={styles.itemDate}>
          {item.createdAt.toLocaleDateString('cs-CZ')} {item.createdAt.toLocaleTimeString('cs-CZ', { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </Text>
      </View>
      <View style={styles.itemIndex}>
        <Text style={styles.indexText}>{index + 1}</Text>
      </View>
    </TouchableOpacity>
  ), [handleItemPress]);

  /**
   * Render empty state when no items exist
   */
  const renderEmptyState = useCallback(() => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>Žádné položky</Text>
      <Text style={styles.emptyStateSubtitle}>
        Přidejte první položku stisknutím tlačítka níže
      </Text>
    </View>
  ), []);

  /**
   * Render error state
   */
  const renderErrorState = useCallback(() => (
    <View style={styles.errorState}>
      <Text style={styles.errorTitle}>Chyba</Text>
      <Text style={styles.errorMessage}>{state.error}</Text>
      <TouchableOpacity style={styles.retryButton} onPress={loadItems}>
        <Text style={styles.retryButtonText}>Zkusit znovu</Text>
      </TouchableOpacity>
    </View>
  ), [state.error, loadItems]);

  // Show loading indicator while data is being fetched
  if (state.isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Načítání položek...</Text>
      </View>
    );
  }

  // Show error state if there's an error
  if (state.error) {
    return renderErrorState();
  }

  return (
    <View style={styles.container}>
      {/* Header with title and item count */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Seznam položek</Text>
        <Text style={styles.itemCount}>
          {state.items.length} {state.items.length === 1 ? 'položka' : 'položek'}
        </Text>
      </View>

      {/* FlatList with items */}
      <FlatList
        data={state.items}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={state.items.length === 0 ? styles.emptyListContainer : undefined}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={state.isRefreshing}
            onRefresh={handleRefresh}
            colors={['#007AFF']}
            tintColor="#007AFF"
          />
        }
        showsVerticalScrollIndicator={true}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
      />

      {/* Add Item Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={addItem}
        activeOpacity={0.8}
      >
        <Text style={styles.addButtonText}>+ Přidat položku</Text>
      </TouchableOpacity>
    </View>
  );
}

/**
 * StyleSheet for the List Screen component
 * Provides clean, mobile-friendly styling with proper spacing and colors
 */
const styles = StyleSheet.create({
  // Main container
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },

  // Loading state
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6c757d',
    fontWeight: '500',
  },

  // Header section
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#212529',
    marginBottom: 4,
  },
  itemCount: {
    fontSize: 14,
    color: '#6c757d',
    fontWeight: '500',
  },

  // List styling
  list: {
    flex: 1,
  },
  emptyListContainer: {
    flexGrow: 1,
  },

  // List item styling
  listItem: {
    backgroundColor: '#ffffff',
    marginHorizontal: 16,
    marginVertical: 6,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#212529',
    marginBottom: 4,
  },
  itemDate: {
    fontSize: 14,
    color: '#6c757d',
    fontWeight: '400',
  },
  itemIndex: {
    backgroundColor: '#007AFF',
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  indexText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },

  // Empty state
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6c757d',
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 16,
    color: '#adb5bd',
    textAlign: 'center',
    lineHeight: 24,
  },

  // Error state
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#dc3545',
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },

  // Add button
  addButton: {
    backgroundColor: '#007AFF',
    marginHorizontal: 16,
    marginVertical: 16,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  addButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
