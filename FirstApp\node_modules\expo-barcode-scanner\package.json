{"name": "expo-barcode-scanner", "version": "13.0.1", "description": "Allows scanning variety of supported barcodes both as standalone module and as extension for expo-camera. It also allows scanning barcodes from existing images.", "main": "build/BarCodeScanner.js", "types": "build/BarCodeScanner.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["barcode", "barcode-scanner", "react-native", "expo"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-barcode-scanner"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/bar-code-scanner/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"expo-image-loader": "~4.7.0"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "ee4f30ef3b5fa567ad1bf94794197f7683fdd481"}