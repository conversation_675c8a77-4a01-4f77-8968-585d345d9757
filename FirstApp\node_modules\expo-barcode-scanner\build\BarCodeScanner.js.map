{"version": 3, "file": "BarCodeScanner.js", "sourceRoot": "", "sources": ["../src/BarCodeScanner.tsx"], "names": [], "mappings": "AAAA,OAAO,EAEL,gBAAgB,EAEhB,oBAAoB,EACpB,mBAAmB,GACpB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,QAAQ,EAAa,MAAM,cAAc,CAAC;AAEnD,OAAO,wBAAwB,MAAM,4BAA4B,CAAC;AAClE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAE9D,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,wBAAwB,CAAC;AAEvD,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAE9B,IAAI,sBAAsB,GAAG,KAAK,CAAC;AAkHnC;;;;;GAKG;AACH,MAAM,OAAO,cAAe,SAAQ,KAAK,CAAC,SAA8B;IACtE,UAAU,GAA2B,EAAE,CAAC;IACxC,eAAe,GAA2B,EAAE,CAAC;IAE7C,MAAM,CAAC,SAAS,GAAG;QACjB,WAAW;QACX,IAAI;KACL,CAAC;IAEF,MAAM,CAAC,gBAAgB,GAAG;QACxB,IAAI,EAAE,IAAI;KACX,CAAC;IAEF,MAAM,CAAC,YAAY,GAAG;QACpB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;KACzC,CAAC;IAEF,iBAAiB;QACf,IAAI,CAAC,sBAAsB,EAAE;YAC3B,OAAO,CAAC,IAAI,CACV,0MAA0M,CAC3M,CAAC;YACF,sBAAsB,GAAG,IAAI,CAAC;SAC/B;IACH,CAAC;IAED,cAAc;IACd;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,mBAAmB;QAC9B,OAAO,wBAAwB,CAAC,mBAAmB,EAAE,CAAC;IACxD,CAAC;IAED,cAAc;IACd;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,uBAAuB;QAClC,OAAO,wBAAwB,CAAC,uBAAuB,EAAE,CAAC;IAC5D,CAAC;IAED,cAAc;IACd;;;;;;;;OAQG;IACH,MAAM,CAAC,cAAc,GAAG,oBAAoB,CAAC;QAC3C,SAAS,EAAE,cAAc,CAAC,mBAAmB;QAC7C,aAAa,EAAE,cAAc,CAAC,uBAAuB;KACtD,CAAC,CAAC;IAEH,cAAc;IACd;;;;;;;;;OASG;IACH,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,GAAW,EACX,eAAyB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;QAEnD,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE;YAC9C,MAAM,IAAI,mBAAmB,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;SAC3E;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;SAC5F;QAED,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;YACzB,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;gBACzE,yEAAyE;gBACzE,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;aAC3E;YACD,oCAAoC;YACpC,OAAO,MAAM,wBAAwB,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;SAC/E;QAED,+EAA+E;QAC/E,OAAO,MAAM,wBAAwB,CAAC,gBAAgB,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;IAC5E,CAAC;IAED,MAAM;QACJ,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QACxC,OAAO,CACL,CAAC,sBAAsB,CACrB,IAAI,WAAW,CAAC,CAChB,gBAAgB,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,EAC1D,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB,GACd,CAAC,QAAiC,EAAE,EAAE,CACtC,CAAC,EAAE,WAAW,EAAiC,EAAE,EAAE;QACjD,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;QAC7B,IACE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACrD,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,iBAAiB,EAC3D;YACA,OAAO;SACR;QAED,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,WAAW,CAAC,CAAC;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACxC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;SACrD;IACH,CAAC,CAAC;IAEJ;;OAEG;IACH,kBAAkB,CAAC,KAA0B;QAC3C,MAAM,WAAW,GAAwB,EAAE,CAAC;QAE5C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAChD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,cAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE;gBACrE,WAAW,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;aAChE;iBAAM;gBACL,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;aAC1B;SACF;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;;AAGH,OAAO,EAAsB,gBAAgB,EAAyB,CAAC;AACvE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,gBAAgB,EAAE,GACxF,cAAc,CAAC", "sourcesContent": ["import {\n  PermissionResponse,\n  PermissionStatus,\n  PermissionHookOptions,\n  createPermissionHook,\n  UnavailabilityError,\n} from 'expo-modules-core';\nimport * as React from 'react';\nimport { Platform, ViewProps } from 'react-native';\n\nimport ExpoBarCodeScannerModule from './ExpoBarCodeScannerModule';\nimport ExpoBarCodeScannerView from './ExpoBarCodeScannerView';\n\nconst { BarCodeType, Type } = ExpoBarCodeScannerModule;\n\nconst EVENT_THROTTLE_MS = 500;\n\nlet warnedAboutDeprecation = false;\n\n// @needsAudit\n/**\n * Those coordinates are represented in the coordinate space of the barcode source (e.g. when you\n * are using the barcode scanner view, these values are adjusted to the dimensions of the view).\n */\nexport type BarCodePoint = {\n  /**\n   * The `x` coordinate value.\n   */\n  x: number;\n  /**\n   * The `y` coordinate value.\n   */\n  y: number;\n};\n\n// @needsAudit\nexport type BarCodeSize = {\n  /**\n   * The height value.\n   */\n  height: number;\n  /**\n   * The width value.\n   */\n  width: number;\n};\n\n// @needsAudit\nexport type BarCodeBounds = {\n  /**\n   * The origin point of the bounding box.\n   */\n  origin: BarCodePoint;\n  /**\n   * The size of the bounding box.\n   */\n  size: BarCodeSize;\n};\n\n// @needsAudit\nexport type BarCodeScannerResult = {\n  /**\n   * The barcode type.\n   */\n  type: string;\n  /**\n   * The parsed information encoded in the bar code.\n   */\n  data: string;\n  /**\n   * The raw information encoded in the bar code.\n   * May be different from `data` depending on the barcode type.\n   * @platform android\n   * @hidden\n   */\n  raw?: string;\n  /**\n   * The [BarCodeBounds](#barcodebounds) object.\n   * `bounds` in some case will be representing an empty rectangle.\n   * Moreover, `bounds` doesn't have to bound the whole barcode.\n   * For some types, they will represent the area used by the scanner.\n   */\n  bounds: BarCodeBounds;\n  /**\n   * Corner points of the bounding box.\n   * `cornerPoints` is not always available and may be empty. On iOS, for `code39` and `pdf417`\n   * you don't get this value.\n   */\n  cornerPoints: BarCodePoint[];\n};\n\n// @docsMissing\nexport type BarCodeEvent = BarCodeScannerResult & {\n  target?: number;\n};\n\n// @docsMissing\nexport type BarCodeEventCallbackArguments = {\n  nativeEvent: BarCodeEvent;\n};\n\n// @docsMissing\nexport type BarCodeScannedCallback = (params: BarCodeEvent) => void;\n\n// @needsAudit\nexport type BarCodeScannerProps = ViewProps & {\n  /**\n   * Camera facing. Use one of `BarCodeScanner.Constants.Type`. Use either `Type.front` or `Type.back`.\n   * Same as `Camera.Constants.Type`.\n   * @default Type.back\n   */\n  type?: 'front' | 'back' | number;\n  /**\n   * An array of bar code types. Usage: `BarCodeScanner.Constants.BarCodeType.<codeType>` where\n   * `codeType` is one of these [listed above](#supported-formats). Defaults to all supported bar\n   * code types. It is recommended to provide only the bar code formats you expect to scan to\n   * minimize battery usage.\n   *\n   * For example: `barCodeTypes={[BarCodeScanner.Constants.BarCodeType.qr]}`.\n   */\n  barCodeTypes?: string[];\n  /**\n   * A callback that is invoked when a bar code has been successfully scanned. The callback is\n   * provided with an [BarCodeScannerResult](#barcodescannerresult).\n   * > __Note:__ Passing `undefined` to the `onBarCodeScanned` prop will result in no scanning. This\n   * > can be used to effectively \"pause\" the scanner so that it doesn't continually scan even after\n   * > data has been retrieved.\n   */\n  onBarCodeScanned?: BarCodeScannedCallback;\n};\n\n/**\n * @deprecated\n * BarCodeScanner has been deprecated and will be removed in a future SDK version. Use `expo-camera` instead.\n * See [How to migrate from `expo-barcode-scanner` to `expo-camera`](https://expo.fyi/barcode-scanner-to-expo-camera)\n * for more details.\n */\nexport class BarCodeScanner extends React.Component<BarCodeScannerProps> {\n  lastEvents: { [key: string]: any } = {};\n  lastEventsTimes: { [key: string]: any } = {};\n\n  static Constants = {\n    BarCodeType,\n    Type,\n  };\n\n  static ConversionTables = {\n    type: Type,\n  };\n\n  static defaultProps = {\n    type: Type.back,\n    barCodeTypes: Object.values(BarCodeType),\n  };\n\n  componentDidMount(): void {\n    if (!warnedAboutDeprecation) {\n      console.warn(\n        'BarCodeScanner has been deprecated and will be removed in a future SDK version. Please use `expo-camera` instead. See https://expo.fyi/barcode-scanner-to-expo-camera for more details on how to migrate'\n      );\n      warnedAboutDeprecation = true;\n    }\n  }\n\n  // @needsAudit\n  /**\n   * Checks user's permissions for accessing the camera.\n   * @return Return a promise that fulfills to an object of type [`PermissionResponse`](#permissionresponse).\n   */\n  static async getPermissionsAsync(): Promise<PermissionResponse> {\n    return ExpoBarCodeScannerModule.getPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Asks the user to grant permissions for accessing the camera.\n   *\n   * On iOS this will require apps to specify the `NSCameraUsageDescription` entry in the `Info.plist`.\n   * @return Return a promise that fulfills to an object of type [`PermissionResponse`](#permissionresponse).\n   */\n  static async requestPermissionsAsync(): Promise<PermissionResponse> {\n    return ExpoBarCodeScannerModule.requestPermissionsAsync();\n  }\n\n  // @needsAudit\n  /**\n   * Check or request permissions for the barcode scanner.\n   * This uses both `requestPermissionAsync` and `getPermissionsAsync` to interact with the permissions.\n   *\n   * @example\n   * ```ts\n   * const [permissionResponse, requestPermission] = BarCodeScanner.usePermissions();\n   * ```\n   */\n  static usePermissions = createPermissionHook({\n    getMethod: BarCodeScanner.getPermissionsAsync,\n    requestMethod: BarCodeScanner.requestPermissionsAsync,\n  });\n\n  // @needsAudit\n  /**\n   * Scan bar codes from the image given by the URL.\n   * @param url URL to get the image from.\n   * @param barCodeTypes An array of bar code types. Defaults to all supported bar code types on\n   * the platform.\n   * > __Note:__ Only QR codes are supported on iOS.\n   * @return A possibly empty array of objects of the `BarCodeScannerResult` shape, where the type\n   * refers to the bar code type that was scanned and the data is the information encoded in the bar\n   * code.\n   */\n  static async scanFromURLAsync(\n    url: string,\n    barCodeTypes: string[] = Object.values(BarCodeType)\n  ): Promise<BarCodeScannerResult[]> {\n    if (!ExpoBarCodeScannerModule.scanFromURLAsync) {\n      throw new UnavailabilityError('expo-barcode-scanner', 'scanFromURLAsync');\n    }\n    if (Array.isArray(barCodeTypes) && !barCodeTypes.length) {\n      throw new Error('No barCodeTypes specified; provide at least one barCodeType for scanner');\n    }\n\n    if (Platform.OS === 'ios') {\n      if (Array.isArray(barCodeTypes) && !barCodeTypes.includes(BarCodeType.qr)) {\n        // Only QR type is supported on iOS, fail if one tries to use other types\n        throw new Error('Only QR type is supported by scanFromURLAsync() on iOS');\n      }\n      // on iOS use only supported QR type\n      return await ExpoBarCodeScannerModule.scanFromURLAsync(url, [BarCodeType.qr]);\n    }\n\n    // On other platforms, if barCodeTypes is not provided, use all available types\n    return await ExpoBarCodeScannerModule.scanFromURLAsync(url, barCodeTypes);\n  }\n\n  render() {\n    const nativeProps = this.convertNativeProps(this.props);\n    const { onBarCodeScanned } = this.props;\n    return (\n      <ExpoBarCodeScannerView\n        {...nativeProps}\n        onBarCodeScanned={this.onObjectDetected(onBarCodeScanned)}\n      />\n    );\n  }\n\n  /**\n   * @hidden\n   */\n  onObjectDetected =\n    (callback?: BarCodeScannedCallback) =>\n    ({ nativeEvent }: BarCodeEventCallbackArguments) => {\n      const { type } = nativeEvent;\n      if (\n        this.lastEvents[type] &&\n        this.lastEventsTimes[type] &&\n        JSON.stringify(nativeEvent) === this.lastEvents[type] &&\n        Date.now() - this.lastEventsTimes[type] < EVENT_THROTTLE_MS\n      ) {\n        return;\n      }\n\n      if (callback) {\n        callback(nativeEvent);\n        this.lastEventsTimes[type] = new Date();\n        this.lastEvents[type] = JSON.stringify(nativeEvent);\n      }\n    };\n\n  /**\n   * @hidden\n   */\n  convertNativeProps(props: BarCodeScannerProps) {\n    const nativeProps: BarCodeScannerProps = {};\n\n    for (const [key, value] of Object.entries(props)) {\n      if (typeof value === 'string' && BarCodeScanner.ConversionTables[key]) {\n        nativeProps[key] = BarCodeScanner.ConversionTables[key][value];\n      } else {\n        nativeProps[key] = value;\n      }\n    }\n\n    return nativeProps;\n  }\n}\n\nexport { PermissionResponse, PermissionStatus, PermissionHookOptions };\nexport const { Constants, getPermissionsAsync, requestPermissionsAsync, scanFromURLAsync } =\n  BarCodeScanner;\n"]}