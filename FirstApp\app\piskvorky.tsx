import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from 'expo-router';
import {
  TicTacToeGameState,
  GameStatistics,
  GameBoard,
  Player,
  CellState,
  WINNING_COMBINATIONS,
  GAME_CONFIG,
  GAME_MESSAGES,
  CheckWinFunction,
  CheckDrawFunction,
} from '../types/TicTacToe';

/**
 * Piškvorky Zrzouna - Tic-Tac-Toe Game Screen
 * React Native implementation of the classic Tic-Tac-Toe game
 * Features touch interactions, game statistics, and Czech localization
 */
export default function PiskvorkyScreen() {
  // Game state management
  const [gameState, setGameState] = useState<TicTacToeGameState>({
    gameBoard: ['', '', '', '', '', '', '', '', ''] as GameBoard,
    currentPlayer: GAME_CONFIG.STARTING_PLAYER,
    gameActive: false,
    gameStatus: 'welcome',
    winner: null,
    winningCells: null,
    result: 'ongoing',
  });

  // Game statistics state
  const [gameStats, setGameStats] = useState<GameStatistics>({
    xWins: 0,
    oWins: 0,
    draws: 0,
    totalGames: 0,
  });

  /**
   * Check if there's a winning combination on the board
   * Returns winner information and winning cell indices
   */
  const checkWin: CheckWinFunction = useCallback((board: GameBoard) => {
    for (const combination of WINNING_COMBINATIONS) {
      const [a, b, c] = combination;
      if (board[a] && board[a] === board[b] && board[a] === board[c]) {
        return {
          hasWinner: true,
          winner: board[a] as Player,
          winningCells: [a, b, c],
        };
      }
    }
    return {
      hasWinner: false,
      winner: null,
      winningCells: null,
    };
  }, []);

  /**
   * Check if the game is a draw (all cells filled, no winner)
   */
  const checkDraw: CheckDrawFunction = useCallback((board: GameBoard) => {
    return board.every(cell => cell !== '');
  }, []);

  /**
   * Load game statistics from AsyncStorage
   */
  const loadGameStats = useCallback(async (): Promise<void> => {
    try {
      const storedStats = await AsyncStorage.getItem(GAME_CONFIG.STATS_STORAGE_KEY);
      if (storedStats) {
        const parsedStats: GameStatistics = JSON.parse(storedStats);
        setGameStats(parsedStats);
      }
    } catch (error) {
      console.error('Error loading game statistics:', error);
    }
  }, []);

  /**
   * Save game statistics to AsyncStorage
   */
  const saveGameStats = useCallback(async (stats: GameStatistics): Promise<void> => {
    try {
      await AsyncStorage.setItem(GAME_CONFIG.STATS_STORAGE_KEY, JSON.stringify(stats));
    } catch (error) {
      console.error('Error saving game statistics:', error);
    }
  }, []);

  /**
   * Update game statistics after a game ends
   */
  const updateGameStats = useCallback(async (winner: Player | null): Promise<void> => {
    const newStats: GameStatistics = {
      ...gameStats,
      totalGames: gameStats.totalGames + 1,
    };

    if (winner === 'X') {
      newStats.xWins += 1;
    } else if (winner === 'O') {
      newStats.oWins += 1;
    } else {
      newStats.draws += 1;
    }

    setGameStats(newStats);
    await saveGameStats(newStats);
  }, [gameStats, saveGameStats]);

  /**
   * Start a new game
   * Resets the game board and sets initial state
   */
  const startGame = useCallback((): void => {
    setGameState({
      gameBoard: ['', '', '', '', '', '', '', '', ''] as GameBoard,
      currentPlayer: GAME_CONFIG.STARTING_PLAYER,
      gameActive: true,
      gameStatus: 'playing',
      winner: null,
      winningCells: null,
      result: 'ongoing',
    });
  }, []);

  /**
   * Handle cell press - make a move in the game
   */
  const handleCellPress = useCallback((index: number): void => {
    // Prevent moves if game is not active or cell is already filled
    if (!gameState.gameActive || gameState.gameBoard[index] !== '') {
      return;
    }

    // Create new board with the move
    const newBoard = [...gameState.gameBoard] as GameBoard;
    newBoard[index] = gameState.currentPlayer;

    // Check for win condition
    const winResult = checkWin(newBoard);
    
    if (winResult.hasWinner) {
      // Game won
      setGameState(prev => ({
        ...prev,
        gameBoard: newBoard,
        gameActive: false,
        gameStatus: 'gameOver',
        winner: winResult.winner,
        winningCells: winResult.winningCells,
        result: 'win',
      }));

      // Update statistics
      updateGameStats(winResult.winner);

      // Show win alert
      setTimeout(() => {
        Alert.alert(
          GAME_MESSAGES.CONGRATULATIONS,
          winResult.winner === 'X' ? GAME_MESSAGES.PLAYER_X_WINS : GAME_MESSAGES.PLAYER_O_WINS,
          [
            {
              text: GAME_MESSAGES.PLAY_AGAIN,
              onPress: startGame,
            },
            {
              text: GAME_MESSAGES.MAIN_MENU,
              onPress: backToMenu,
              style: 'cancel',
            },
          ]
        );
      }, 500);

      return;
    }

    // Check for draw condition
    if (checkDraw(newBoard)) {
      // Game is a draw
      setGameState(prev => ({
        ...prev,
        gameBoard: newBoard,
        gameActive: false,
        gameStatus: 'gameOver',
        result: 'draw',
      }));

      // Update statistics
      updateGameStats(null);

      // Show draw alert
      setTimeout(() => {
        Alert.alert(
          GAME_MESSAGES.DRAW,
          GAME_MESSAGES.DRAW_MESSAGE,
          [
            {
              text: GAME_MESSAGES.PLAY_AGAIN,
              onPress: startGame,
            },
            {
              text: GAME_MESSAGES.MAIN_MENU,
              onPress: backToMenu,
              style: 'cancel',
            },
          ]
        );
      }, 500);

      return;
    }

    // Continue game - switch players
    const nextPlayer: Player = gameState.currentPlayer === 'X' ? 'O' : 'X';
    setGameState(prev => ({
      ...prev,
      gameBoard: newBoard,
      currentPlayer: nextPlayer,
    }));
  }, [gameState, checkWin, checkDraw, updateGameStats, startGame]);

  /**
   * Restart the current game
   */
  const restartGame = useCallback((): void => {
    startGame();
  }, [startGame]);

  /**
   * Go back to welcome screen
   */
  const backToMenu = useCallback((): void => {
    setGameState(prev => ({
      ...prev,
      gameActive: false,
      gameStatus: 'welcome',
      gameBoard: ['', '', '', '', '', '', '', '', ''] as GameBoard,
      currentPlayer: GAME_CONFIG.STARTING_PLAYER,
      winner: null,
      winningCells: null,
      result: 'ongoing',
    }));
  }, []);

  /**
   * Load game statistics when screen comes into focus
   */
  useFocusEffect(
    useCallback(() => {
      loadGameStats();
    }, [loadGameStats])
  );

  /**
   * Render individual game cell
   */
  const renderCell = useCallback((value: CellState, index: number) => {
    const isWinning = gameState.winningCells?.includes(index) || false;
    
    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.cell,
          value === 'X' && styles.cellX,
          value === 'O' && styles.cellO,
          isWinning && styles.cellWinning,
        ]}
        onPress={() => handleCellPress(index)}
        activeOpacity={0.7}
        disabled={!gameState.gameActive || value !== ''}
      >
        <Text style={[
          styles.cellText,
          value === 'X' && styles.cellTextX,
          value === 'O' && styles.cellTextO,
        ]}>
          {value}
        </Text>
      </TouchableOpacity>
    );
  }, [gameState.gameActive, gameState.winningCells, handleCellPress]);

  /**
   * Render welcome screen
   */
  const renderWelcomeScreen = useCallback(() => (
    <View style={styles.welcomeContainer}>
      <Text style={styles.title}>{GAME_MESSAGES.TITLE}</Text>
      <TouchableOpacity
        style={styles.playButton}
        onPress={startGame}
        activeOpacity={0.8}
      >
        <Text style={styles.playButtonText}>{GAME_MESSAGES.PLAY_BUTTON}</Text>
      </TouchableOpacity>
      
      {/* Game Statistics */}
      {gameStats.totalGames > 0 && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsTitle}>Statistiky</Text>
          <Text style={styles.statsText}>Celkem her: {gameStats.totalGames}</Text>
          <Text style={styles.statsText}>Výhry X: {gameStats.xWins}</Text>
          <Text style={styles.statsText}>Výhry O: {gameStats.oWins}</Text>
          <Text style={styles.statsText}>Remízy: {gameStats.draws}</Text>
        </View>
      )}
    </View>
  ), [startGame, gameStats]);

  /**
   * Render game screen
   */
  const renderGameScreen = useCallback(() => (
    <View style={styles.gameContainer}>
      {/* Current Player Indicator */}
      <View style={styles.playerIndicator}>
        <Text style={styles.playerText}>
          {GAME_MESSAGES.CURRENT_PLAYER}: 
          <Text style={[
            styles.currentPlayerText,
            gameState.currentPlayer === 'X' ? styles.playerXText : styles.playerOText
          ]}>
            {' '}{gameState.currentPlayer}
          </Text>
        </Text>
      </View>

      {/* Game Board */}
      <View style={styles.gameBoard}>
        {gameState.gameBoard.map((cell, index) => renderCell(cell, index))}
      </View>

      {/* Game Controls */}
      <View style={styles.gameControls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={restartGame}
          activeOpacity={0.8}
        >
          <Text style={styles.controlButtonText}>{GAME_MESSAGES.RESTART_BUTTON}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.controlButton, styles.backButton]}
          onPress={backToMenu}
          activeOpacity={0.8}
        >
          <Text style={styles.controlButtonText}>{GAME_MESSAGES.BACK_TO_MENU}</Text>
        </TouchableOpacity>
      </View>
    </View>
  ), [gameState, renderCell, restartGame, backToMenu]);

  return (
    <SafeAreaView style={styles.container}>
      {gameState.gameStatus === 'welcome' ? renderWelcomeScreen() : renderGameScreen()}
    </SafeAreaView>
  );
}

/**
 * StyleSheet for Piškvorky Zrzouna
 * Orange and white color scheme matching the original web design
 * Mobile-optimized with touch-friendly sizing
 */
const { width: screenWidth } = Dimensions.get('window');
const cellSize = Math.min((screenWidth - 80) / 3, 100);

const styles = StyleSheet.create({
  // Main container
  container: {
    flex: 1,
    backgroundColor: '#ff8c42', // Orange gradient background
  },

  // Welcome screen styles
  welcomeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 40,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  playButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 50,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    marginBottom: 30,
  },
  playButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ff6b1a',
    textAlign: 'center',
  },

  // Statistics styles
  statsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff6b1a',
    marginBottom: 10,
  },
  statsText: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },

  // Game screen styles
  gameContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 20,
  },

  // Player indicator
  playerIndicator: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 16,
    borderRadius: 15,
    marginBottom: 30,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  playerText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  currentPlayerText: {
    fontSize: 22,
    fontWeight: 'bold',
  },
  playerXText: {
    color: '#ff6b1a',
  },
  playerOText: {
    color: '#28a745',
  },

  // Game board
  gameBoard: {
    backgroundColor: '#ffffff',
    padding: 15,
    borderRadius: 20,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    marginBottom: 30,
  },

  // Game cells
  cell: {
    width: cellSize,
    height: cellSize,
    backgroundColor: '#f8f9fa',
    borderWidth: 3,
    borderColor: '#e9ecef',
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cellX: {
    backgroundColor: '#fff5f0',
    borderColor: '#ff6b1a',
  },
  cellO: {
    backgroundColor: '#f0fff4',
    borderColor: '#28a745',
  },
  cellWinning: {
    backgroundColor: '#fff200',
    borderColor: '#ffc107',
    transform: [{ scale: 1.05 }],
  },
  cellText: {
    fontSize: cellSize * 0.5,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cellTextX: {
    color: '#ff6b1a',
  },
  cellTextO: {
    color: '#28a745',
  },

  // Game controls
  gameControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
  },
  controlButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: '#ff6b1a',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    minWidth: 120,
  },
  backButton: {
    borderColor: '#6c757d',
  },
  controlButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ff6b1a',
    textAlign: 'center',
  },
});
