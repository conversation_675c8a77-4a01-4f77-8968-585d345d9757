# React Native List Screen Component

A comprehensive React Native screen component built with Expo and TypeScript that displays and manages a list of items with AsyncStorage persistence.

## 🚀 Features

### Core Functionality
- **FlatList Implementation**: Efficient list rendering with proper key handling
- **AsyncStorage Persistence**: Data persists between app sessions
- **useFocusEffect Integration**: Automatically loads data when screen comes into focus
- **Add Item Functionality**: "Přidat položku" button adds new items to the list
- **TypeScript Support**: Full type safety with comprehensive interfaces

### User Experience
- **Loading States**: Shows loading indicator while fetching data
- **Empty State**: Friendly message when no items exist
- **Error Handling**: Graceful error states with retry functionality
- **Pull-to-Refresh**: Swipe down to refresh the list
- **Visual Feedback**: Success/error alerts for user actions
- **Item Details**: Tap items to view creation details

### Technical Features
- **Expo Router Navigation**: Seamless navigation between screens
- **Clean Architecture**: Separated concerns and modular code
- **Performance Optimized**: FlatList with proper rendering optimizations
- **Mobile-Friendly Styling**: Responsive design with clean UI
- **Czech Localization**: All text in Czech language

## 📁 File Structure

```
FirstApp/
├── app/
│   ├── _layout.tsx          # Navigation layout configuration
│   ├── index.tsx            # Home screen with navigation to list
│   └── list.tsx             # Main list screen component
├── types/
│   └── ListItem.ts          # TypeScript interfaces and types
└── README_ListComponent.md  # This documentation
```

## 🔧 Implementation Details

### TypeScript Interfaces

**ListItem Interface:**
```typescript
interface ListItem {
  id: string;           // Unique identifier
  title: string;        // Display title
  createdAt: Date;      // Creation timestamp
  updatedAt?: Date;     // Optional update timestamp
}
```

**Component State:**
```typescript
interface ListScreenState {
  items: ListItem[];    // Array of list items
  isLoading: boolean;   // Loading state
  error: string | null; // Error state
  isRefreshing: boolean; // Refresh state
}
```

### AsyncStorage Operations

**Storage Key:** `@list_items_storage_key`

**Data Format:** JSON array of ListItem objects with Date objects serialized as ISO strings

**Operations:**
- `loadItems()`: Retrieves and parses items from storage
- `saveItems()`: Serializes and stores items array
- `addItem()`: Creates new item with unique ID and timestamp

### useFocusEffect Implementation

```typescript
useFocusEffect(
  useCallback(() => {
    loadItems();
  }, [loadItems])
);
```

This ensures data is always fresh when navigating to the screen, perfect for scenarios where data might be modified from other parts of the app.

### FlatList Configuration

**Key Features:**
- `keyExtractor`: Uses item.id for proper key handling
- `removeClippedSubviews`: Performance optimization
- `maxToRenderPerBatch`: Limits rendering batch size
- `windowSize`: Controls rendering window
- `RefreshControl`: Pull-to-refresh functionality

## 🎨 Styling

### Design System
- **Primary Color**: #007AFF (iOS blue)
- **Background**: #f8f9fa (light gray)
- **Cards**: White with subtle shadows
- **Typography**: System fonts with proper hierarchy

### Responsive Features
- Proper spacing and padding
- Touch-friendly button sizes
- Readable font sizes
- Consistent visual hierarchy

## 🔄 State Management

### Loading States
1. **Initial Load**: Shows loading spinner
2. **Refresh**: Pull-to-refresh indicator
3. **Error**: Error message with retry button
4. **Empty**: Friendly empty state message

### Error Handling
- Network/storage errors are caught and displayed
- Retry functionality for failed operations
- User-friendly error messages in Czech

## 📱 User Interactions

### Navigation
- Home screen button navigates to list
- Back button returns to home
- Proper header configuration

### List Interactions
- **Tap Item**: Shows item details in alert
- **Pull Down**: Refreshes the list
- **Add Button**: Creates new item with auto-generated title

### Feedback
- Success alerts for successful operations
- Error alerts for failed operations
- Loading indicators during async operations

## 🛠️ Dependencies

### Required Packages
- `@react-native-async-storage/async-storage`: Data persistence
- `expo-router`: Navigation system
- `react-native`: Core React Native components

### Expo Compatibility
- Built for Expo SDK 53+
- No native linking required
- Works with Expo Go and development builds

## 🚀 Usage

### Running the App
```bash
cd FirstApp
npm install
npx expo start
```

### Navigation
1. Start from home screen (`app/index.tsx`)
2. Tap "📝 Otevřít seznam položek" button
3. Navigate to list screen (`app/list.tsx`)

### Adding Items
1. Tap "Přidat položku" button
2. New item appears at top of list
3. Item is automatically saved to AsyncStorage

## 🔍 Code Organization

### Separation of Concerns
- **Data Layer**: AsyncStorage operations
- **State Management**: React hooks and state
- **UI Layer**: React Native components
- **Navigation**: Expo Router configuration

### Code Comments
- Comprehensive JSDoc comments
- Inline explanations for complex logic
- Clear function and variable naming

### Error Boundaries
- Try-catch blocks for async operations
- Graceful degradation for failures
- User-friendly error messages

## 🎯 Best Practices Implemented

1. **TypeScript**: Full type safety
2. **Performance**: Optimized FlatList rendering
3. **UX**: Loading states and error handling
4. **Accessibility**: Proper component structure
5. **Maintainability**: Clean, documented code
6. **Persistence**: Reliable data storage
7. **Navigation**: Proper routing setup

## 🔄 Future Enhancements

Potential improvements that could be added:
- Item editing functionality
- Item deletion with swipe gestures
- Search and filtering
- Item categories or tags
- Export/import functionality
- Offline synchronization
